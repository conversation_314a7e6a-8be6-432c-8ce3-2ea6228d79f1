import { Connection, WSMessage } from 'agents';
import {
  BaseWebSocketEvent,
  FormSubmissionEvent as WebSocketFormSubmissionEvent,
} from '../types/websocket-events';
import { CropBoxUpdateData } from '../types/agent-state';
import { <PERSON><PERSON>and<PERSON> } from './form-handler';

/**
 * WebSocket Event Handler Module
 * Handles all WebSocket event routing and processing
 */
export class WebSocketHandler {
  constructor(
    private formHandler: FormHandler,
    private log: (...args: any[]) => void,
    private broadcast: (message: string, excludeConnections?: string[]) => void,
    private handleFlowInitiate: (params: { platform: string }) => Promise<void>,
    private handleTermsDecline: () => Promise<void>,
    private handleCropBoxUpdate: (data: CropBoxUpdateData) => Promise<void>,
    private handleRetry: () => Promise<void>,
  ) {}

  /**
   * Process incoming WebSocket messages
   */
  async onMessage(connection: Connection, message: WSMessage): Promise<void> {
    if (typeof message !== 'string') {
      console.error('Invalid message type received:', typeof message);
      return;
    }

    try {
      const event = JSON.parse(message) as BaseWebSocketEvent;
      this.log(`Received WebSocket event: ${event.type}`);
      await this.handleWebSocketEvent(event, connection);
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
      throw new Error(
        `Failed to process WebSocket message: ${message}, Error: ${JSON.stringify(error)}`,
      );
    }
  }

  /**
   * Handle WebSocket events with type safety and consistent routing
   */
  private async handleWebSocketEvent(
    event: BaseWebSocketEvent,
    connection: Connection,
  ): Promise<void> {
    switch (event.type) {
      case 'agree_and_continue':
        await this.handleAgreeAndContinue();
        break;

      case 'decline_terms':
        await this.handleTermsDecline();
        break;

      case 'form_submission':
        await this.handleFormSubmission(event as WebSocketFormSubmissionEvent);
        break;

      case 'cropbox-update':
        await this.handleCropBoxUpdateEvent(event as CropBoxUpdateData);
        break;

      case 'retry':
        await this.handleRetry();
        break;

      default:
        this.handleUnknownEvent(event, connection);
        break;
    }
  }

  /**
   * Handle agree and continue events
   */
  private async handleAgreeAndContinue(): Promise<void> {
    // This would need platform information from the parent class
    // For now, we'll delegate to the parent's handleFlowInitiate method
    await this.handleFlowInitiate({
      platform: 'kazeel', // This should come from state
    });
  }

  /**
   * Handle form submission events
   */
  private async handleFormSubmission(formEvent: WebSocketFormSubmissionEvent): Promise<void> {
    await this.formHandler.handleInputSubmitted(formEvent);
  }

  /**
   * Handle crop box update events
   */
  private async handleCropBoxUpdateEvent(cropEvent: CropBoxUpdateData): Promise<void> {
    await this.handleCropBoxUpdate(cropEvent);
  }

  /**
   * Handle unknown events
   */
  private handleUnknownEvent(event: BaseWebSocketEvent, connection: Connection): void {
    this.log('Received unknown event type:', event.type);
    this.broadcast(JSON.stringify({ ...event }), [connection.id]);
  }

  /**
   * Validate WebSocket event structure
   */
  private validateEvent(event: BaseWebSocketEvent): boolean {
    return event && typeof event.type === 'string';
  }

  /**
   * Get supported event types
   */
  getSupportedEventTypes(): string[] {
    return [
      'agree_and_continue',
      'decline_terms',
      'form_submission',
      'cropbox-update',
      'retry',
    ];
  }

  /**
   * Check if event type is supported
   */
  isEventTypeSupported(eventType: string): boolean {
    return this.getSupportedEventTypes().includes(eventType);
  }
}
