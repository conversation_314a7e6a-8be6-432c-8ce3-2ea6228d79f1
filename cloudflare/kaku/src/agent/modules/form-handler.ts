import { Action } from '../types/extract-result';
import { FormSubmissionPayload } from '../types/form-types';
import { AgentState } from '../types';
import { ElementCoordinateMapping } from '../services/coordinate-resolution';
import { encryptData, getEncryptionKey } from '../../common/utils';
import {
  FormSubmissionEvent as WorkflowFormSubmissionEvent,
  FormSubmissionPayloadSource,
} from '../../workflow/types/ConnectionsWorkflowParams';

/**
 * Form Handler Module
 * Handles all form submission related functionality including validation,
 * action creation, and workflow communication using callback pattern
 */
export class FormHandler {
  private config = {
    debug: true, // Set to true for verbose logging
  };

  constructor(
    private env: Env,
    private callbacks: {
      onFormSubmissionComplete: () => void;
      onFormSubmissionError: (error: unknown) => void;
      onCoordinateResolutionNeeded: () => Promise<void>;
    },
  ) {}

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][form-handler]', ...args);
    }
  }

  /**
   * Main form submission handler that orchestrates the entire process
   * Uses callback pattern to communicate with parent agent
   */
  async handleInputSubmitted(
    payload: FormSubmissionPayload,
    state: AgentState,
    coordinateResolutionPromise?: Promise<void>,
  ): Promise<void> {
    try {
      if (!this.isReadyForSubmission(state.status)) return;

      this.validatePayload(payload);

      this.log(
        `[FORM SUBMISSION] Type: ${payload.interaction}, Clicked Element: ${payload.clickId}`,
      );

      // Wait for coordinate resolution if needed
      if (coordinateResolutionPromise) {
        await this.callbacks.onCoordinateResolutionNeeded();
      }

      const actions = this.createFormActions(payload, state);

      this.validateActions(actions);

      actions.sort((a, b) => a.order - b.order);
      await this.sendActionsToWorkflow(actions, state);

      // Notify parent that form submission is complete
      this.callbacks.onFormSubmissionComplete();
    } catch (error) {
      this.log('Failed to handle form submission:', error);
      this.callbacks.onFormSubmissionError(error);
    }
  }

  /**
   * Check if the agent is ready to process form submissions
   */
  private isReadyForSubmission(status: AgentState['status']): boolean {
    return !(status === 'waiting-for-human');
  }

  /**
   * Validate the form submission payload
   */
  private validatePayload(payload: FormSubmissionPayload): void {
    const { clickId, interaction } = payload;

    if (!clickId) {
      throw new Error('Missing required field: clickId');
    }
    if (!interaction) {
      throw new Error('Missing required field: interaction');
    }
  }

  /**
   * Create actions based on form submission payload
   */
  private createFormActions(payload: FormSubmissionPayload, state: AgentState): Action[] {
    const { clickId, interaction, ...formValues } = payload;
    const actions: Action[] = [];

    if (interaction === 'submit') {
      this.addFormFieldActions(formValues, actions, state);
      this.addSubmitAction(clickId, actions, state);
    } else if (interaction === 'click') {
      this.addClickAction(clickId, actions, state);
    }

    return actions;
  }

  /**
   * Add form field actions (fill and select) to the actions array
   */
  private addFormFieldActions(
    formValues: Record<string, string>,
    actions: Action[],
    state: AgentState,
  ): void {
    Object.entries(formValues).forEach(([fieldId, value]) => {
      if (!value) return;

      // Check if this is a radio button selection (fieldId-optionValue pattern)
      const radioActionName = `${fieldId}-${value}`;
      if (state.elementCoordinateMapping?.[radioActionName]) {
        const fieldOrder = this.getFieldOrder(radioActionName, state);
        actions.push({
          type: 'select',
          name: radioActionName,
          value,
          coordinates: state.elementCoordinateMapping[radioActionName],
          order: fieldOrder,
          isSubmitAction: false,
        });
      } else if (state.elementCoordinateMapping?.[fieldId]) {
        const fieldOrder = this.getFieldOrder(fieldId, state);
        actions.push({
          type: 'fill',
          name: fieldId,
          value,
          coordinates: state.elementCoordinateMapping[fieldId],
          order: fieldOrder,
          isSubmitAction: false,
        });
      }
    });
  }

  /**
   * Add submit action to the actions array
   */
  private addSubmitAction(clickId: string, actions: Action[], state: AgentState): void {
    if (state.elementCoordinateMapping?.[clickId]) {
      const submitOrder = this.getFieldOrder(clickId, state);
      actions.push({
        type: 'click',
        name: clickId,
        coordinates: state.elementCoordinateMapping[clickId],
        order: submitOrder,
        isSubmitAction: true,
      });
    }
  }

  /**
   * Add click action to the actions array
   */
  private addClickAction(clickId: string, actions: Action[], state: AgentState): void {
    if (state.elementCoordinateMapping?.[clickId]) {
      const clickOrder = this.getFieldOrder(clickId, state);
      actions.push({
        type: 'click',
        name: clickId,
        coordinates: state.elementCoordinateMapping[clickId],
        order: clickOrder,
        isSubmitAction: false,
      });
    }
  }

  /**
   * Validate that all actions have required coordinates
   */
  private validateActions(actions: Action[]): void {
    const actionsWithMissingCoordinates = actions.filter((action) => !action.coordinates);
    if (actionsWithMissingCoordinates.length > 0) {
      const missingActionNames = actionsWithMissingCoordinates
        .map((action) => action.name)
        .join(', ');
      throw new Error(`Actions missing coordinates: ${missingActionNames}`);
    }
  }

  /**
   * Helper method to get field order from extraction result
   */
  private getFieldOrder(elementId: string, state: AgentState): number {
    const { fields, buttons } = state.pageStateResult!.extractionResult.controls;

    const field = fields.find((f) => f.id === elementId || elementId.startsWith(`${f.id}-`));
    if (field) {
      return field.order;
    }

    const button = buttons.find((b) => b.id === elementId);
    if (button) {
      return button.order;
    }

    return 999;
  }

  /**
   * Helper method to send actions to workflow
   */
  private async sendActionsToWorkflow(actions: Action[], state: AgentState): Promise<void> {
    const encryptionKey = await getEncryptionKey();
    const encrypted = await encryptData(JSON.stringify({ actions }), encryptionKey);

    const workflow = await this.env.CONNECTIONS_WORKFLOW.get(state.workflowId!);
    const payloadWithCoordinates: WorkflowFormSubmissionEvent = {
      payload: encrypted,
      coordinates: state.elementCoordinateMapping,
      source: FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION,
    };

    await workflow.sendEvent({
      type: 'form-submission',
      payload: payloadWithCoordinates,
    });
  }
}
