import { Action } from '../types/extract-result';
import { FormSubmissionPayload } from '../types/form-types';
import { PageStatus } from '../types/agent-state';
import { AgentState } from '../types';
import { ElementCoordinateMapping } from '../services/coordinate-resolution';
import { ErrorService } from '../../common/error';
import { encryptData, getEncryptionKey } from '../../common/utils';
import {
  FormSubmissionEvent as WorkflowFormSubmissionEvent,
  FormSubmissionPayloadSource,
} from '../../workflow/types/ConnectionsWorkflowParams';

/**
 * Form Handler Module
 * Handles all form submission related functionality including validation,
 * action creation, and workflow communication
 */
export class FormHandler {
  constructor(
    private state: AgentState,
    private setState: (newState: AgentState) => void,
    private log: (...args: any[]) => void,
    private env: Env,
    private targetSessionId: string | null,
  ) {}

  /**
   * Main form submission handler that orchestrates the entire process
   */
  async handleInputSubmitted(payload: FormSubmissionPayload): Promise<void> {
    try {
      if (!this.isReadyForSubmission()) return;

      this.validatePayload(payload);

      this.setState({ ...this.state, status: PageStatus.WAITING_FOR_AGENT });

      if (this.state.coordinateResolutionPromise) {
        await this.waitForCoordinateResolution();
      }

      const actions = this.createFormActions(payload);

      this.validateActions(actions);

      actions.sort((a, b) => a.order - b.order);
      await this.sendActionsToWorkflow(actions);
    } catch (error) {
      await this.handleFormSubmissionError(error);
    }
  }

  /**
   * Check if the agent is ready to process form submissions
   */
  private isReadyForSubmission(): boolean {
    if (this.state.status !== 'waiting-for-human') return false;
    if (!this.state.workflowId) {
      throw new Error('workflowID should not be null');
    }
    return true;
  }

  /**
   * Validate the form submission payload
   */
  private validatePayload(payload: FormSubmissionPayload): void {
    const { clickId, interaction } = payload;

    if (!clickId) {
      throw new Error('Missing required field: clickId');
    }
    if (!interaction) {
      throw new Error('Missing required field: interaction');
    }
  }

  /**
   * Wait for coordinate resolution to complete
   */
  private async waitForCoordinateResolution(): Promise<void> {
    this.log('[FORM SUBMISSION] Waiting for coordinate resolution to complete...');
    await this.state.coordinateResolutionPromise;
    this.log('[FORM SUBMISSION] Coordinate resolution completed');
  }

  /**
   * Create actions based on form submission payload
   */
  private createFormActions(payload: FormSubmissionPayload): Action[] {
    const { clickId, interaction, ...formValues } = payload;
    const actions: Action[] = [];

    if (interaction === 'submit') {
      this.addFormFieldActions(formValues, actions);
      this.addSubmitAction(clickId, actions);
    } else if (interaction === 'click') {
      this.addClickAction(clickId, actions);
    }

    return actions;
  }

  /**
   * Add form field actions (fill and select) to the actions array
   */
  private addFormFieldActions(formValues: Record<string, string>, actions: Action[]): void {
    Object.entries(formValues).forEach(([fieldId, value]) => {
      if (!value) return;

      // Check if this is a radio button selection (fieldId-optionValue pattern)
      const radioActionName = `${fieldId}-${value}`;
      if (this.state.elementCoordinateMapping?.[radioActionName]) {
        const fieldOrder = this.getFieldOrder(radioActionName);
        actions.push({
          type: 'select',
          name: radioActionName,
          value,
          coordinates: this.state.elementCoordinateMapping[radioActionName],
          order: fieldOrder,
          isSubmitAction: false,
        });
      } else if (this.state.elementCoordinateMapping?.[fieldId]) {
        const fieldOrder = this.getFieldOrder(fieldId);
        actions.push({
          type: 'fill',
          name: fieldId,
          value,
          coordinates: this.state.elementCoordinateMapping[fieldId],
          order: fieldOrder,
          isSubmitAction: false,
        });
      }
    });
  }

  /**
   * Add submit action to the actions array
   */
  private addSubmitAction(clickId: string, actions: Action[]): void {
    if (this.state.elementCoordinateMapping?.[clickId]) {
      const submitOrder = this.getFieldOrder(clickId);
      actions.push({
        type: 'click',
        name: clickId,
        coordinates: this.state.elementCoordinateMapping[clickId],
        order: submitOrder,
        isSubmitAction: true,
      });
    }
  }

  /**
   * Add click action to the actions array
   */
  private addClickAction(clickId: string, actions: Action[]): void {
    if (this.state.elementCoordinateMapping?.[clickId]) {
      const clickOrder = this.getFieldOrder(clickId);
      actions.push({
        type: 'click',
        name: clickId,
        coordinates: this.state.elementCoordinateMapping[clickId],
        order: clickOrder,
        isSubmitAction: false,
      });
    }
  }

  /**
   * Validate that all actions have required coordinates
   */
  private validateActions(actions: Action[]): void {
    const actionsWithMissingCoordinates = actions.filter((action) => !action.coordinates);
    if (actionsWithMissingCoordinates.length > 0) {
      const missingActionNames = actionsWithMissingCoordinates
        .map((action) => action.name)
        .join(', ');
      throw new Error(`Actions missing coordinates: ${missingActionNames}`);
    }
  }

  /**
   * Helper method to get field order from extraction result
   */
  private getFieldOrder(elementId: string): number {
    const { fields, buttons } = this.state.pageStateResult!.extractionResult.controls;

    const field = fields.find((f) => f.id === elementId || elementId.startsWith(`${f.id}-`));
    if (field) {
      return field.order;
    }

    const button = buttons.find((b) => b.id === elementId);
    if (button) {
      return button.order;
    }

    return 999;
  }

  /**
   * Helper method to send actions to workflow
   */
  private async sendActionsToWorkflow(actions: Action[]): Promise<void> {
    const encryptionKey = await getEncryptionKey();
    const encrypted = await encryptData(JSON.stringify({ actions }), encryptionKey);

    const workflow = await this.env.CONNECTIONS_WORKFLOW.get(this.state.workflowId!);
    const payloadWithCoordinates: WorkflowFormSubmissionEvent = {
      payload: encrypted,
      coordinates: this.state.elementCoordinateMapping,
      source: FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION,
    };

    await workflow.sendEvent({
      type: 'form-submission',
      payload: payloadWithCoordinates,
    });
  }

  /**
   * Handle form submission errors with proper error service integration
   */
  private async handleFormSubmissionError(error: unknown): Promise<void> {
    console.error('Failed to handle form submission:', error);
    await ErrorService.handleGenericError(
      'agent',
      'SYSTEM_ERROR',
      error,
      {
        sessionId: this.targetSessionId ?? 'not_initialized',
        userId: this.state.userId,
        platformId: this.state.platformId,
        referenceId: this.state.referenceId,
      },
      this.env,
      'error',
    );
  }
}
