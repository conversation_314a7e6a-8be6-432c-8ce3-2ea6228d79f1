import { CDP } from '../../browser/simple-cdp';
import { AgentState } from '../types';
import { PageStatus } from '../types/agent-state';
import {
  PageStateResult,
  PageStateResultWithOptionalCoordinates,
} from '../types/extract-result';
import {
  CoordinateResolutionService,
  ElementCoordinateRequest,
  ElementCoordinateMapping,
} from '../services/coordinate-resolution';
import { FormVisionResult } from '../../form-generation/htmx-generator';

/**
 * State Manager Module
 * Handles all state management operations including form state changes,
 * coordinate resolution, and state lifecycle management
 */
export class StateManager {
  constructor(
    private state: AgentState,
    private setState: (newState: AgentState) => void,
    private log: (...args: any[]) => void,
    private cdpClient: CDP | null,
    private targetSessionId: string | null,
    private coordinateResolutionService: CoordinateResolutionService,
  ) {}

  /**
   * Handle form state changes from workflow
   */
  async onFormStateChange(screenshot: string, result: PageStateResult): Promise<void> {
    const isAuthenticated = result.classificationResult.screenInfo.authState === 'authenticated';
    const newStatus: AgentState['status'] = isAuthenticated
      ? PageStatus.COMPLETED
      : PageStatus.WAITING_FOR_HUMAN;

    const pageStateResult: PageStateResultWithOptionalCoordinates = {
      ...result,
      coordinatesResolved: false,
    };

    this.setState({
      ...this.state,
      status: newStatus,
      pageStateResult,
      history: this.state?.history ? [...this.state.history, result] : [result],
    });

    // Start coordinate resolution in background if not authenticated
    if (!isAuthenticated) {
      await this.startCoordinateResolution(screenshot, result.extractionResult);
    }

    this.log(`Form state updated. New status: ${newStatus}`);
  }

  /**
   * Start coordinate resolution process in background
   */
  private async startCoordinateResolution(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<void> {
    try {
      this.log('Starting coordinate resolution in background...');
      
      const coordinateResolutionPromise = this.resolveElementCoordinates(
        screenshot,
        formVisionResult,
      );

      // Store the promise in state for form submission to wait on
      this.setState({
        ...this.state,
        coordinateResolutionPromise,
      });

      // Resolve coordinates and update state
      const elementCoordinateMapping = await coordinateResolutionPromise;
      
      this.setState({
        ...this.state,
        elementCoordinateMapping,
        pageStateResult: {
          ...this.state.pageStateResult!,
          coordinatesResolved: true,
        },
        coordinateResolutionPromise: undefined,
      });

      this.log('Coordinate resolution completed and state updated');
    } catch (error) {
      this.log('Error during coordinate resolution:', error);
      // Clear the promise on error
      this.setState({
        ...this.state,
        coordinateResolutionPromise: undefined,
      });
    }
  }

  /**
   * Resolve element coordinates and return the mapping
   */
  private async resolveElementCoordinates(
    screenshot: string,
    formVisionResult: FormVisionResult,
  ): Promise<ElementCoordinateMapping> {
    if (!this.cdpClient || !this.targetSessionId) {
      throw new Error('CDP client or target session not available for coordinate resolution');
    }

    const viewport = await this.cdpClient.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewportWidth = viewport.cssLayoutViewport.clientWidth;
    const viewportHeight = viewport.cssLayoutViewport.clientHeight;

    const elementRequest: ElementCoordinateRequest = {
      fields: formVisionResult.controls.fields,
      buttons: formVisionResult.controls.buttons,
    };

    return await this.coordinateResolutionService.resolveElementCoordinates(
      screenshot,
      elementRequest,
      this.state.platformId,
      viewportWidth,
      viewportHeight,
    );
  }

  /**
   * Update agent initialization status
   */
  updateInitializationStatus(status: AgentState['initializationStatus']): void {
    this.setState({
      ...this.state,
      initializationStatus: status,
    });
    this.log(`Initialization status updated to: ${status}`);
  }

  /**
   * Update page status
   */
  updatePageStatus(status: PageStatus): void {
    this.setState({
      ...this.state,
      status,
    });
    this.log(`Page status updated to: ${status}`);
  }

  /**
   * Update interactivity status
   */
  updateInteractivityStatus(
    status: 'enabled' | 'disabled',
    cropBox?: any,
    inputBoxRects?: any[],
  ): void {
    this.setState({
      ...this.state,
      interactivity: {
        status,
        cropBox,
        inputBoxRects,
      },
    });
    this.log(`Interactivity status updated to: ${status}`);
  }

  /**
   * Update terms and conditions approval status
   */
  updateTermsApproval(approved: boolean): void {
    this.setState({
      ...this.state,
      termsAndConditionsApproved: approved,
    });
    this.log(`Terms and conditions approval updated to: ${approved}`);
  }

  /**
   * Update workflow ID
   */
  updateWorkflowId(workflowId: string): void {
    this.setState({
      ...this.state,
      workflowId,
    });
    this.log(`Workflow ID updated to: ${workflowId}`);
  }

  /**
   * Update session ID
   */
  updateSessionId(sessionId: string): void {
    this.setState({
      ...this.state,
      sessionId,
    });
    this.log(`Session ID updated to: ${sessionId}`);
  }

  /**
   * Clear coordinate resolution promise
   */
  clearCoordinateResolutionPromise(): void {
    this.setState({
      ...this.state,
      coordinateResolutionPromise: undefined,
    });
  }

  /**
   * Get current state
   */
  getCurrentState(): AgentState {
    return this.state;
  }

  /**
   * Check if coordinates are resolved
   */
  areCoordinatesResolved(): boolean {
    return this.state.pageStateResult?.coordinatesResolved === true;
  }

  /**
   * Check if coordinate resolution is in progress
   */
  isCoordinateResolutionInProgress(): boolean {
    return this.state.coordinateResolutionPromise !== undefined;
  }

  /**
   * Reset state to initial values (for cleanup)
   */
  resetToInitialState(initialState: AgentState): void {
    this.setState(initialState);
    this.log('State reset to initial values');
  }
}
