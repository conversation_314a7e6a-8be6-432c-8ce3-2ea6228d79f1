import { CDP } from '../../browser/simple-cdp';
import {
  CDPAttachedToTargetParams,
  CDPConsoleAPIParams,
  CDPEvent,
  CDPRuntimeBindingCalledParams,
  CDPRuntimeExceptionParams,
} from '../../browser/types/cdp-events';
import { startCaptchaMonitoring } from '../../browser';
import { AgentState } from '../types';
import { PageStatus } from '../types/agent-state';
import { ErrorCollector, ErrorRouter } from '../../common/error';

/**
 * CDP Manager Module
 * Handles all Chrome DevTools Protocol related functionality including
 * browser session management, error monitoring, and captcha handling
 */
export class CDPManager {
  private cdpErrorHandlers: (() => void) | null = null;

  constructor(
    private cdpClient: CDP | null,
    private state: AgentState,
    private setState: (newState: AgentState) => void,
    private log: (...args: any[]) => void,
    private targetSessionId: string | null,
    private name: string,
    private handleAndDisplayError: (error: any) => void,
  ) {}

  /**
   * Handle target attachment events from CDP
   */
  async onAttachedToTarget({ params }: CDPEvent<CDPAttachedToTargetParams>): Promise<void> {
    const { sessionId, targetInfo } = params;

    if (targetInfo.type === 'page') {
      // Update target session ID in parent class
      this.updateTargetSessionId(sessionId);
      await this.cdpClient!.Page.enable(undefined, sessionId);
      await this.cdpClient!.Runtime.enable(undefined, sessionId);
    }
  }

  /**
   * Set up CDP error monitoring with runtime exception and console API handlers
   */
  setupCDPErrorMonitoring(): void {
    if (!this.cdpClient) return;

    // Use agent state for user/platform info instead of parsing agent name
    const userId = this.state.userId;
    const platformId = this.state.platformId;
    const referenceId = this.state.referenceId;

    const handleRuntimeException = (event: CDPEvent<CDPRuntimeExceptionParams>) => {
      const exceptionDetails = event.params?.exceptionDetails;

      if (!exceptionDetails.text.includes('kazeel')) return;

      this.log(`[CDP] [DO: ${this.name}] Runtime exception:`, JSON.stringify(event, null, 2));

      const errorContext = ErrorCollector.collectError(
        'cdp',
        'SCRIPT_INJECTION_FAILED',
        event,
        'error',
        {
          userId,
          platformId,
          referenceId,
          sessionId: this.state.sessionId || 'unknown',
        },
      );

      const classifiedError = ErrorRouter.classifyError(errorContext);
      this.handleAndDisplayError(classifiedError);
    };

    const handleConsoleAPI = (event: CDPEvent<CDPConsoleAPIParams>) => {
      // Extract console API details from event.params (simple-cdp event structure)
      const consoleType = event.params?.type;
      const exceptionDetails = event.params?.exceptionDetails;

      if (consoleType === 'error' && !exceptionDetails?.text?.includes('kazeel')) return;

      if (consoleType === 'error') {
        this.log(
          `[CDP] [DO: ${this.name}] Console error detected:`,
          JSON.stringify(event, null, 2),
        );

        const errorContext = ErrorCollector.collectError(
          'cdp',
          'SCRIPT_INJECTION_FAILED',
          event,
          'error',
          {
            userId,
            platformId,
            referenceId,
            sessionId: this.state.sessionId || 'unknown',
          },
        );

        const classifiedError = ErrorRouter.classifyError(errorContext);
        this.handleAndDisplayError(classifiedError);
      }
    };

    // Set up CDP event listeners
    this.cdpClient.Runtime.addEventListener('exceptionThrown', handleRuntimeException);
    this.cdpClient.Runtime.addEventListener('consoleAPICalled', handleConsoleAPI);

    this.cdpErrorHandlers = () => {
      if (this.cdpClient) {
        this.cdpClient.Runtime.removeEventListener('exceptionThrown', handleRuntimeException);
        this.cdpClient.Runtime.removeEventListener('consoleAPICalled', handleConsoleAPI);
      }
    };

    this.log(`[Agent] [DO: ${this.name}] CDP error monitoring enabled`);
  }

  /**
   * Clean up CDP error monitoring
   */
  cleanupCDPErrorMonitoring(): void {
    this.log(`[Agent] [DO: ${this.name}] CDP error monitoring disabled`);
    if (this.cdpErrorHandlers) {
      this.cdpErrorHandlers();
      this.cdpErrorHandlers = null;
    }
  }

  /**
   * Set up captcha monitoring with runtime bindings
   */
  async setupCaptchaMonitoring(executionContextId: number): Promise<void> {
    if (!this.cdpClient || !this.targetSessionId) {
      throw new Error('CDP client or target session not available');
    }

    const bindingListener = async ({ params }: CDPEvent<CDPRuntimeBindingCalledParams>) => {
      if (params.name === '__captchaSolved__') {
        try {
          const payload = JSON.parse(params.payload);
          if (payload.type === 'CAPTCHA_SOLVED') {
            this.log(`Received CAPTCHA_SOLVED via Binding`);
            // Call the captcha solved handler
            await this.handleCaptchaSolvedEvent({
              differencePercentage: payload.differencePercentage,
              timestamp: payload.timestamp,
              executionContextId,
              source: payload.source,
            });
          } else {
            console.warn(
              'Received unexpected payload type on __captchaSolved__ binding:',
              payload.type,
            );
          }
        } catch (parseError) {
          console.error(
            'Failed to parse payload from __captchaSolved__ binding:',
            params,
            parseError,
          );
        }
        return;
      }

      console.warn(`Received unhandled binding call: ${JSON.stringify(params)}`);
    };

    try {
      await this.cdpClient.Runtime.addBinding(
        {
          name: '__captchaSolved__',
          executionContextId,
        },
        this.targetSessionId,
      );
      this.log('Added Runtime binding for captcha solved notifications.');

      this.cdpClient.Runtime.removeEventListener('bindingCalled', bindingListener);
      this.cdpClient.Runtime.addEventListener('bindingCalled', bindingListener);
      this.log('Attached listener for Runtime.bindingCalled');
    } catch (bindingError) {
      console.error('Failed to set up one or more Runtime bindings:', bindingError);
      return;
    }

    this.setState({
      ...this.state,
      status: PageStatus.WAITING_FOR_HUMAN,
      interactivity: {
        ...this.state.interactivity!,
        status: 'enabled',
      },
      captchaSetupComplete: true,
    });

    await startCaptchaMonitoring(
      this.cdpClient,
      {
        diffThreshold: 5,
        screenshotQuality: 90,
      },
      executionContextId,
      this.targetSessionId,
    );
  }

  /**
   * Handle captcha solved events
   */
  private async handleCaptchaSolvedEvent(event: {
    differencePercentage: number;
    timestamp: number;
    executionContextId: number;
    source: string;
  }): Promise<void> {
    this.log(
      `[CAPTCHA SOLVED] Difference: ${event.differencePercentage}%, Source: ${event.source}`,
    );

    // Update state to indicate captcha completion
    this.setState({
      ...this.state,
      status: PageStatus.COMPLETED,
      interactivity: {
        ...this.state.interactivity!,
        status: 'completed',
      },
    });
  }

  /**
   * Update target session ID (to be called from parent class)
   */
  private updateTargetSessionId(sessionId: string): void {
    // This would need to be handled by the parent class
    // For now, we'll log it
    this.log(`Target session ID updated: ${sessionId}`);
  }

  /**
   * Get current CDP client
   */
  getCDPClient(): CDP | null {
    return this.cdpClient;
  }

  /**
   * Update CDP client reference
   */
  updateCDPClient(cdpClient: CDP | null): void {
    this.cdpClient = cdpClient;
  }
}
