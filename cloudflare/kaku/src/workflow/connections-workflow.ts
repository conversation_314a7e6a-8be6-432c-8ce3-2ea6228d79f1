import { WorkflowEntrypoint, WorkflowStep, type WorkflowEvent } from 'cloudflare:workers';
import {
  FORM_VISION_CLASSIFICATION_PROMPT_V34,
  FORM_VISION_PROMPT_V6,
} from '../../test/scripts/geminiPerformance';
import { ElementCoordinateMapping } from '../agent/services/coordinate-resolution';
import {
  Action,
  ActionWithOptionalCoordinates,
  PageStateResult,
} from '../agent/types/extract-result';
import {
  getHashedScriptUrl,
  initRTCSteaming,
  initScreenCropper,
  initTensorFlowDetector,
  injectScript,
  injectTensorFlowJS,
  setupInputFocusListener,
  wrapForMainFrameOnly,
} from '../browser';
import { ErrorService } from '../common/error';
import { decryptData, getEncryptionKey } from '../common/utils';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../common/utils/storeConnectionScreenshotToR2';
import { FormV<PERSON>Result } from '../form-generation/htmx-generator';
import { GeminiLLMRepository } from '../llm/GeminiLLMRepository';
import { LLMService } from '../llm/LLMService';
import { OpenAILLMRepository } from '../llm/OpenAILLMRepository';
import { platformDetails, PlatformTypes } from '../ui/constants';
import { CDPBrowserDataAdapter } from './adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from './BrowserStateService';
import { R2BrowserStateRepository } from './R2BrowserStateRepository';
import { BrowserServiceFactory, BrowserSession, RemoteBrowserService } from './services';
import {
  ConnectionsWorkflowParams,
  FormSubmissionEvent,
  FormSubmissionEventPayload,
  FormSubmissionPayloadSource,
} from './types/ConnectionsWorkflowParams';
import { WorkflowStepName } from './types/WorkflowStepName';
import {
  defaultWorkflowNoRetryConfig,
  defaultWorkflowRetryConfig,
  getPlatformVersion,
  K_CUSTOM_VIEWPORT,
  pageStateResultComparisonPromptInstructions,
  PlatformDetectionConfig,
  platformDetectionConfigs,
  twoFactorChangeDetectionConfig,
} from './utils/constants';
import { compareScreenshots, sleep, withTimeout } from './utils/helpers';
import { makeParallelLLMCalls } from '../llm/llm-parallel-calls';
import { CDP } from '../browser/simple-cdp';
import { StateChangeType } from './types/StateChangeType';
import { ClassificationResult, FormField } from '../form-generation/types';
import { CoordinatorDOBrowserStateRepository } from './CoordinatorDOBrowserStateRepository';

export class ConnectionsWorkflow extends WorkflowEntrypoint<Env, ConnectionsWorkflowParams> {
  private cdp?: CDP;

  //target tab props
  private targetSessionId?: string;
  private targetId?: string;
  private executionContextId?: number;

  //control tab props
  private controlTabSessionId?: string;
  private controlTabTargetId?: string;

  private browserSession?: BrowserSession;
  private preSubmissionScreenshot: string | null = null;
  private platformId?: PlatformTypes;

  // Logging configuration
  private config = {
    debug: true, // Set to true for verbose logging
  };

  private llmService: LLMService = new LLMService({
    primaryRepo: new GeminiLLMRepository(this.env.GEMINI_API_KEY, this.env.AI_GATEWAY_GEMINI_URL),
    secondaryRepo: new OpenAILLMRepository(this.env.OPENAI_API_KEY, this.env.AI_GATEWAY_OPENAI_URL),
  });

  private browserStateService: BrowserStateService = new BrowserStateService(
    //new R2BrowserStateRepository(this.env.SCREENSHOTS_INBOUND_BUCKET)
    new CoordinatorDOBrowserStateRepository(this.env.CoordinatorDO),
  );
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );

  private stateChangeAbortController = new AbortController();

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][connections-workflow]', ...args);
    }
  }

  private warn(...args: any[]): void {
    console.warn('[kazeel][connections-workflow]', ...args);
  }

  private error(...args: any[]): void {
    console.error('[kazeel][connections-workflow]', ...args);
  }

  async run(event: WorkflowEvent<ConnectionsWorkflowParams>, step: WorkflowStep) {
    // Store platformId for use in detection strategies
    this.platformId = event.payload.platformId;

    const browserWsEndpoint: string = await (async () => {
      try {
        const endpoint = await this.setupBrowserSession(event.payload.sessionId);

        //Load browser state, if it exists
        if (this.cdp) {
          const browserDataAdapter = new CDPBrowserDataAdapter(this.cdp, this.targetSessionId);
          await this.browserStateService.loadBrowserStateToPage(
            browserDataAdapter,
            event.payload.userId,
            event.payload.platformId,
          );
        }

        return endpoint;
      } catch (e) {
        this.error('An error occurred while setting up the browser session:', e);
        if (e instanceof Error) {
          await ErrorService.handleBrowserConnectionError(
            e,
            {
              ...event.payload,
              referenceId: event.payload.linkId,
            },
            this.env,
          );
        }
        throw e;
      }
    })();

    try {
      await step.do(
        WorkflowStepName.SETUP_VIEWPORT_AND_DEVICE_METRICS,
        defaultWorkflowNoRetryConfig,
        async () => {
          await this.ensureViewportSettings();
          await setupInputFocusListener(this.cdp!, this.targetSessionId);
        },
      );
    } catch (e) {
      await this.handleWorkflowStepError(
        WorkflowStepName.SETUP_VIEWPORT_AND_DEVICE_METRICS,
        e,
        event.payload,
      );
      throw e;
    }

    try {
      await step.do(
        WorkflowStepName.INITIALIZE_SESSION_AND_CAPTURE_SCREENSHOT,
        defaultWorkflowNoRetryConfig,
        async () => {
          this.setupExecutionContextListener();
          await this.navigateToLoginPage(event.payload.platformId);
        },
      );
    } catch (e) {
      await this.handleWorkflowStepError(
        WorkflowStepName.INITIALIZE_SESSION_AND_CAPTURE_SCREENSHOT,
        e,
        event.payload,
      );
      throw e;
    }
    // Step 2: screenshot
    const screenshot = await (async () => {
      try {
        return await step.do(
          WorkflowStepName.INITIALIZE_SESSION_AND_CAPTURE_SCREENSHOT,
          defaultWorkflowNoRetryConfig,
          async () => {
            return await this.captureScreenshot();
          },
        );
      } catch (e) {
        await this.handleWorkflowStepError(
          WorkflowStepName.INITIALIZE_SESSION_AND_CAPTURE_SCREENSHOT,
          e,
          event.payload,
        );
        throw e;
      }
    })();

    let isCompleted = false;
    let captchaScreenUpdated = false;
    let latestScreenshot = screenshot;

    while (!isCompleted) {
      // Step 3: Generate form with OpenAI
      let pageStateResult = await (async () => {
        try {
          return await step.do(
            WorkflowStepName.GENERATE_FORM_HTMX,
            defaultWorkflowRetryConfig,
            async () => {
              return await this.generateFormWithOpenAI(latestScreenshot, event.payload.platformId);
            },
          );
        } catch (e) {
          await this.handleWorkflowStepError(WorkflowStepName.GENERATE_FORM_HTMX, e, event.payload);
          throw e;
        }
      })();

      const pageRequiresExternalFormSubmission = this.checkIfScreenIsATwoFactorAuthScreen(
        pageStateResult.classificationResult,
      );

      if (pageStateResult.classificationResult.screenInfo.screenClass === 'loading-screen') {
        this.log('→ Page is loading, waiting for completion and capturing new screenshot');

        const newScreenshot = await (async () => {
          try {
            return await step.do(
              WorkflowStepName.SCREENSHOT_AFTER_LOADING,
              defaultWorkflowNoRetryConfig,
              async () => {
                return await this.captureScreenshot();
              },
            );
          } catch (e) {
            await this.handleWorkflowStepError(
              WorkflowStepName.SCREENSHOT_AFTER_LOADING,
              e,
              event.payload,
            );
            throw e;
          }
        })();

        latestScreenshot = newScreenshot;
        this.log('✓ Page loading completed, screenshot updated');
        continue;
      }
      if (pageStateResult.classificationResult.screenInfo.screenClass === 'captcha-screen') {
        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.OnCaptcha,
          latestScreenshot,
        );

        await step.do(
          WorkflowStepName.INJECT_SCRIPT,
          defaultWorkflowRetryConfig,
          async () => await this.injectTargetTabScriptsForCaptcha(event.payload.linkId),
        );
        let isCaptchaSolved = false;
        while (!isCaptchaSolved) {
          const viewport = { width: K_CUSTOM_VIEWPORT.width, height: K_CUSTOM_VIEWPORT.height };

          try {
            await step.do(
              WorkflowStepName.SEND_CAPTCHA_DETECTION_RESPONSE,
              defaultWorkflowNoRetryConfig,
              async () => {
                const { cdp } = this;

                if (!cdp) throw new Error('CDP client not initialized');

                const agentName = `${event.payload.linkId}`;
                const agentId = this.env.Connections.idFromName(agentName);
                const agent = this.env.Connections.get(agentId);
                if (!captchaScreenUpdated) {
                  await initRTCSteaming(
                    { cdpSession: cdp },
                    this.executionContextId!,
                    this.targetSessionId!,
                    viewport,
                  );
                }
                await agent.handleCaptchaDetected(this.executionContextId!, viewport);
              },
            );
          } catch (error) {
            await this.handleWorkflowStepError(
              WorkflowStepName.SEND_CAPTCHA_DETECTION_RESPONSE,
              error,
              event.payload,
            );
            throw error;
          }

          this.log('→ Waiting for captcha solved notification');
          const captchaSolvedEvent = await step.waitForEvent<{
            differencePercentage: number;
            timestamp: string;
            source?: string;
          }>('Await captcha solved', {
            type: 'captcha-solved',
            timeout: '3 minutes',
          });

          this.log('✓ Received captcha solved event', {
            differencePercentage: captchaSolvedEvent.payload.differencePercentage,
          });

          try {
            await step.do(
              WorkflowStepName.PAUSE_INTERACTIVITY,
              defaultWorkflowRetryConfig,
              async () => {
                this.log('✓ Interactivity paused');
                const agentName = `${event.payload.linkId}`;
                const agentId = this.env.Connections.idFromName(agentName);
                const agent = this.env.Connections.get(agentId);
                await agent.stopInteractivity();
              },
            );
          } catch (e) {
            await this.handleWorkflowStepError(
              WorkflowStepName.PAUSE_INTERACTIVITY,
              e,
              event.payload,
            );
            throw e;
          }
          const newScreenshot = await this.captureScreenshot();

          pageStateResult = await (async () => {
            try {
              return await step.do(
                WorkflowStepName.VERIFY_CAPTCHA_STATUS,
                defaultWorkflowRetryConfig,
                async () => {
                  //return await this.generateFormWithTwoCallsInParallel(step, event, latestScreenshot);
                  return await this.generateFormWithOpenAI(newScreenshot, event.payload.platformId);
                },
              );
            } catch (e) {
              await this.handleWorkflowStepError(
                WorkflowStepName.VERIFY_CAPTCHA_STATUS,
                e,
                event.payload,
              );
              throw e;
            }
          })();
          if (pageStateResult.classificationResult.screenInfo.screenClass !== 'captcha-screen') {
            this.log('✓ Captcha solved, continuing with flow');
            isCaptchaSolved = true;

            const agentName = `${event.payload.linkId}`;
            const agentId = this.env.Connections.idFromName(agentName);
            const agent = this.env.Connections.get(agentId);

            latestScreenshot = newScreenshot;
          } else {
            captchaScreenUpdated = true;
            this.log('→ Captcha still active, continuing to monitor');
          }
        }
      }

      // Step 4: Acknowledge extracted form (Phase 1 + start Phase 2)
      try {
        await step.do(
          WorkflowStepName.ACKNOWLEDGE_EXTRACTED_FORM,
          defaultWorkflowNoRetryConfig,
          async () => {
            await this.acknowledgeExtractedForm(
              `${event.payload.linkId}`,
              pageStateResult,
              latestScreenshot,
            );
          },
        );
      } catch (e) {
        await this.handleWorkflowStepError(
          WorkflowStepName.ACKNOWLEDGE_EXTRACTED_FORM,
          e,
          event.payload,
        );
        throw e;
      }

      if (pageStateResult.classificationResult.screenInfo.authState === 'authenticated') {
        await this.handleOnAuthenticated(event, latestScreenshot);
        isCompleted = true;
        const coordinatorId = this.env.CoordinatorDO.idFromName(event.payload.userId);
        const coordinatorStub = this.env.CoordinatorDO.get(coordinatorId);
        await coordinatorStub.markPlatformConnected(event.payload.linkId);
        await this.cleanupResources();

        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.Authenticated,
          latestScreenshot,
        );

        break;
      }

      if (pageRequiresExternalFormSubmission) {
        //We start listening for when a user will authenticate using external methods
        //First, Capture pre-submission screenshot for visual change detection
        this.preSubmissionScreenshot = await this.captureScreenshotForComparison();
        this.waitForPageUpdateAfterSubmission(
          twoFactorChangeDetectionConfig,
          pageStateResult.extractionResult,
        ).then(async (pageChangeTypeResponse) => {
          if (pageChangeTypeResponse != null) {
            this.log(`Detected 2-factor state change by type ${pageChangeTypeResponse}`);
            //There was a state change from external source, send a form-submission to let the flow resume
            const connectionDOName = event.payload.linkId;

            const agent = this.env.Connections.idFromName(connectionDOName);
            const stub = this.env.Connections.get(agent);
            stub.sendTwoFactorAuthenticationCompletedEvent();
          }
        });
      }

      // Step 5: Wait for user form input
      const formSubmissionEvent = await step.waitForEvent<FormSubmissionEvent>(
        WorkflowStepName.AWAIT_USER_FORM_INPUT,
        {
          type: 'form-submission',
          timeout: '10 minutes',
        },
      );

      if (formSubmissionEvent.payload.source == FormSubmissionPayloadSource.PAGE_FORM_SUBMISSION) {
        this.log('Received Form submission event from user filling the form');
        if (pageRequiresExternalFormSubmission) {
          //We need to cancel the listeners for external form submission
          this.stateChangeAbortController.abort();
        }
        // Step 6: Execute form actions
        try {
          await step.do(
            WorkflowStepName.EXECUTE_FORM_ACTIONS,
            defaultWorkflowNoRetryConfig,
            async () => {
              await this.executeAIActions(pageStateResult, formSubmissionEvent.payload.coordinates);
              return await this.executeFormActions(formSubmissionEvent.payload.payload);
            },
          );
        } catch (e) {
          await this.handleWorkflowStepError(
            WorkflowStepName.EXECUTE_FORM_ACTIONS,
            e,
            event.payload,
          );
          throw e;
        }

        // Step 7: Wait for page update after submission
        try {
          const configSettings: PlatformDetectionConfig = platformDetectionConfigs[this.platformId];
          await step.do(
            WorkflowStepName.WAIT_FOR_PAGE_UPDATE_AFTER_SUBMISSION,
            defaultWorkflowNoRetryConfig,
            async () => {
              return await this.waitForPageUpdateAfterSubmission(
                configSettings,
                pageStateResult.extractionResult,
              );
            },
          );
        } catch (e) {
          await this.handleWorkflowStepError(
            WorkflowStepName.WAIT_FOR_PAGE_UPDATE_AFTER_SUBMISSION,
            e,
            event.payload,
          );
          throw e;
        }
      } else if (
        formSubmissionEvent.payload.source ==
        FormSubmissionPayloadSource.TWO_FACTOR_AUTHENTICATION_COMPLETION
      ) {
        this.log(
          `Change detected from user acknowledging from external source, skipping form actions step`,
        );
        const agent = this.env.Connections.idFromName(event.payload.linkId);
        const stub = this.env.Connections.get(agent);
        await stub.markWaitingForAgent();
      }

      const screenshot = await (async () => {
        try {
          return await step.do(
            WorkflowStepName.TAKE_SCREENSHOT,
            defaultWorkflowNoRetryConfig,
            async () => {
              return await this.captureScreenshot();
            },
          );
        } catch (e) {
          await this.handleWorkflowStepError(WorkflowStepName.TAKE_SCREENSHOT, e, event.payload);
          throw e;
        }
      })();

      latestScreenshot = screenshot;

      //Save the screenshot to R2
      storeConnectionScreenshotToR2(
        this.env.SCREENSHOTS_INBOUND_BUCKET,
        event.payload.userId,
        event.payload.platformId,
        event.payload.sessionId,
        ConnectionWorkflowState.UserFormFilled,
        latestScreenshot,
      );
    }
  }

  private setupExecutionContextListener() {
    if (!this.cdp) throw new Error('CDP client not initialized');

    // remove promise. I just want to update teh executioncontextId
    this.cdp.Runtime.addEventListener('executionContextCreated', (fullData: { params: any }) => {
      const { context } = fullData.params;
      if (context.name === 'kaku-target-world' && context.auxData.frameId === this.targetId) {
        this.executionContextId = context.id;
        this.cdp?.Runtime.removeEventListener('executionContextCreated', (e) => {});
      }
    });
  }

  private async setupBrowserSession(sessionId: string): Promise<string> {
    const bbSession = await this.browserService.getSession(sessionId);
    this.browserSession = bbSession;

    this.cdp = new CDP({ webSocketDebuggerUrl: bbSession.wsEndpoint });

    try {
      await this.cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      // Set up two-tab architecture
      await this.setupTwoTabArchitecture();
      await this.injectControlTabScriptsEarly(bbSession.wsEndpoint);
      await this.injectBrowserControllerProxyInTarget();
    } catch (error) {
      this.error('Failed to set up browser session:', error);
      throw error;
    }

    return bbSession.wsEndpoint;
  }

  /**
   * Sets up the two-tab architecture with control and target tabs
   */
  private async setupTwoTabArchitecture(): Promise<void> {
    if (!this.cdp) throw new Error('CDP client not initialized');

    // Track attached targets
    const attachedTargets: Array<{ sessionId: string; targetId: string; type: string }> = [];

    // Set up event listener for target attachment
    const targetAttachedPromise = new Promise<void>((resolve) => {
      const handler = ({ params }: { params: any }) => {
        const { sessionId, targetInfo } = params;
        if (targetInfo.type === 'page') {
          attachedTargets.push({
            sessionId,
            targetId: targetInfo.targetId,
            type: targetInfo.type,
          });

          // When we have both tabs, assign them appropriately
          if (attachedTargets.length === 2) {
            // First tab becomes control tab, second becomes target tab
            this.controlTabSessionId = attachedTargets[0].sessionId;
            this.controlTabTargetId = attachedTargets[0].targetId;
            this.targetSessionId = attachedTargets[1].sessionId;
            this.targetId = attachedTargets[1].targetId;

            this.cdp?.Target.removeEventListener('attachedToTarget', handler);
            resolve();
          }
        }
      };
      this.cdp?.Target.addEventListener('attachedToTarget', handler);
    });

    // Create control tab first
    await this.cdp.Target.createTarget({ url: 'about:blank' });

    // Create target tab
    await this.cdp.Target.createTarget({ url: 'about:blank' });

    // Wait for both targets to be attached
    await targetAttachedPromise;

    // Enable required domains for both tabs
    await this.enableDomainsForBothTabs();
  }

  /**
   * Enable required CDP domains for both control and target tabs
   */
  private async enableDomainsForBothTabs(): Promise<void> {
    if (!this.cdp || !this.controlTabSessionId || !this.targetSessionId) {
      throw new Error('CDP client or session IDs not initialized');
    }

    // Enable domains for control tab
    await Promise.all([
      this.cdp.Page.enable(undefined, this.controlTabSessionId),
      this.cdp.Runtime.enable(undefined, this.controlTabSessionId),
    ]);

    // Enable domains for target tab
    await Promise.all([
      this.cdp.Page.enable(undefined, this.targetSessionId),
      this.cdp.Runtime.enable(undefined, this.targetSessionId),
    ]);

    // Disable content security policy for both tabs
    await Promise.all([
      this.cdp.Page.setBypassCSP({ enabled: true }, this.controlTabSessionId),
      this.cdp.Page.setBypassCSP({ enabled: true }, this.targetSessionId),
    ]);

    await Promise.all([this.cdp.WebAuthn.enable(undefined, this.targetSessionId)]);

    const virtualAuthenticatorOptions = {
      protocol: 'ctap2' as const,
      transport: 'internal' as const,
      hasResidentKey: true,
      hasUserVerification: true,
      isUserVerified: true,
      automaticPresenceSimulation: true,
    };
    await this.cdp.WebAuthn.addVirtualAuthenticator(
      { options: virtualAuthenticatorOptions },
      this.targetSessionId,
    );
  }

  private async injectBrowserControllerProxyInTarget() {
    const browserControllerProxyUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'browser-controller-proxy.min.js',
    );
    const piiRedactorUrl = getHashedScriptUrl(this.env.KAKU_API_ENDPOINT, 'pii-redactor.min.js');

    const [browserControllerProxyScript, piiRedactorScript] = await Promise.all([
      fetch(browserControllerProxyUrl).then((r) => r.text()),
      fetch(piiRedactorUrl).then((r) => r.text()),
    ]);

    // These scripts will persist across page navigations
    await Promise.all([
      this.cdp!.Page.addScriptToEvaluateOnNewDocument(
        {
          source: piiRedactorScript,
          worldName: 'kaku-target-world',
        },
        this.targetSessionId,
      ),
      this.cdp!.Page.addScriptToEvaluateOnNewDocument(
        {
          source: browserControllerProxyScript,
          worldName: 'kaku-target-world',
        },
        this.targetSessionId,
      ),
    ]);
  }

  /**
   * Inject control tab scripts early using addScriptToEvaluateOnNewDocument
   * This ensures scripts persist across page navigations
   */
  private async injectControlTabScriptsEarly(browserWsEndpoint: string): Promise<void> {
    if (!this.cdp || !this.controlTabSessionId || !this.controlTabTargetId) {
      throw new Error('Control tab not properly initialized');
    }

    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const persistentCDPControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'persistent-cdp-controller.min.js',
    );

    try {
      // Fetch script contents for injection
      const [crossTabCommScript, persistentCDPControllerScript] = await Promise.all([
        fetch(crossTabCommUrl).then((r) => r.text()),
        fetch(persistentCDPControllerUrl).then((r) => r.text()),
      ]);

      // the delay is added to wait for the persistentCDP Controller script to be available
      const initScript = `
        (async () => {
          while (!window.persistentCDPController) {
            await new Promise(resolve => setTimeout(resolve, 10));
          }
          await window.persistentCDPController.init(
            '${browserWsEndpoint}',
            '${this.targetId}'
          );

        })();
      `;

      // Inject scripts using addScriptToEvaluateOnNewDocument for persistence
      await Promise.all([
        this.cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(crossTabCommScript),
            worldName: 'kaku-control-world',
          },
          this.controlTabSessionId,
        ),
        this.cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(persistentCDPControllerScript),
            worldName: 'kaku-control-world',
          },
          this.controlTabSessionId,
        ),
        this.cdp.Page.addScriptToEvaluateOnNewDocument(
          {
            source: wrapForMainFrameOnly(initScript),
            worldName: 'kaku-control-world',
          },
          this.controlTabSessionId,
        ),
      ]);
    } catch (error) {
      this.error('Error injecting control tab scripts early:', error);
      throw error;
    }
  }

  private async navigateToLoginPage(platformId: PlatformTypes): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    // goto github.com on controlTapSessionId
    const pageLoadControlTab = this.waitForPageLoad();
    await this.cdp.Page.navigate(
      {
        url: platformDetails[platformId].loginLink,
      },
      this.controlTabSessionId,
    );
    await pageLoadControlTab;

    const pageLoad = this.waitForPageLoad();

    await this.cdp.Page.navigate(
      {
        url: platformDetails[platformId].loginLink,
      },
      this.targetSessionId,
    );

    await pageLoad;
  }

  private async waitForPageLoad(): Promise<boolean | null> {
    if (!this.cdp || !this.targetSessionId) return Promise.resolve(null);
    return await new Promise<boolean | null>((resolve) => {
      const handler = () => {
        this.log('Page Loaded event fired', { pageLoaded: true });
        resolve(true);
      };
      this.cdp?.Page.addEventListener('loadEventFired', handler);

      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        console.log('Page load State change listener cancelled by abort controller');
        this.cdp?.Page.removeEventListener('loadEventFired', handler);
        resolve(null);
      });
    });
  }

  /**
   * Ensures the viewport and device metrics are set to the correct dimensions
   * This should be called after navigation or any time we need to guarantee viewport consistency
   */
  private async ensureViewportSettings(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    await this.cdp.Emulation.setDeviceMetricsOverride(
      {
        //store these to constants
        width: K_CUSTOM_VIEWPORT.width,
        height: K_CUSTOM_VIEWPORT.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      this.targetSessionId,
    );
  }

  private async captureScreenshot(redactPII: boolean = false): Promise<string> {
    if (!this.cdp || !this.targetSessionId) return '';

    if (redactPII) {
      try {
        const result = await this.cdp.Runtime.evaluate(
          {
            expression: `
              (async () => {
                try {
                 await window.browserController.init();
                 return await window.browserController.captureScreenshotWithRedaction();
                } catch (error) {
                  return {
                    success: false,
                    error: error.message,
                    fallback: true
                  };
                }
              })()
            `,
            awaitPromise: true,
            returnByValue: true,
            contextId: this.executionContextId!,
          },
          this.targetSessionId,
        );

        if (result.result.value?.success) {
          const screenshotData = result.result.value.data;
          const sizeKB = (screenshotData.length * 0.75) / 1024;
          const processingTime = result.result.value.processingTime || 0;

          this.log(
            `✓ Screenshot with PII redaction captured: ${sizeKB.toFixed(2)} KB (${processingTime.toFixed(2)}ms)`,
          );

          if (result.result.value.redactionApplied && result.result.value.redactionStats) {
            const stats = result.result.value.redactionStats;
            this.log(`  📊 PII Redaction Stats:`);
            this.log(`    - Items redacted: ${stats.redactedCount || 0}`);
            this.log(`    - Redaction time: ${stats.processingTime?.toFixed(2) || 'N/A'}ms`);

            if (stats.mappings && stats.mappings.length > 0) {
              const elementTypes = stats.mappings.reduce((acc: any, mapping: any) => {
                acc[mapping.elementType] = (acc[mapping.elementType] || 0) + 1;
                return acc;
              }, {});

              this.log(
                `    - Element types: ${Object.entries(elementTypes)
                  .map(([type, count]) => `${type}(${count})`)
                  .join(', ')}`,
              );
            }

            // Performance check
            if (stats.processingTime > 500) {
              this.warn(
                `    ⚠️  PII redaction exceeded 500ms target: ${stats.processingTime.toFixed(2)}ms`,
              );
            }

            // Restoration stats if available
            if (stats.restorationStats) {
              const restoreStats = stats.restorationStats;
              this.log(`    - Items restored: ${restoreStats.restoredCount || 0}`);
              if (restoreStats.failedCount > 0) {
                this.warn(`    ⚠️  Restoration failures: ${restoreStats.failedCount}`);
              }
              if (restoreStats.errors && restoreStats.errors.length > 0) {
                this.warn(
                  `    ⚠️  Restoration errors: ${restoreStats.errors.slice(0, 3).join(', ')}${restoreStats.errors.length > 3 ? '...' : ''}`,
                );
              }
            }
          } else {
            this.log(`  📊 PII Redaction: Not applied or no stats available`);
          }

          return screenshotData;
        } else {
          this.warn(
            'PII redaction screenshot failed, falling back to standard screenshot:',
            result.result.value?.error,
          );
        }
      } catch (error) {
        this.warn(
          'Error during PII redaction screenshot, falling back to standard screenshot:',
          error,
        );
      }
    } else {
      this.log('📷 Taking standard screenshot (PII redaction disabled)');
    }

    const screenshot = await this.cdp.Page.captureScreenshot(
      {
        format: 'png',
        captureBeyondViewport: false,
      },
      this.targetSessionId,
    );

    await this.cdp.Page.getLayoutMetrics(undefined, this.targetSessionId);

    return screenshot.data;
  }

  /**
   * Inject target tab scripts for captcha handling
   * Control tab scripts are already injected early, so only inject target tab scripts
   */
  private async injectTargetTabScriptsForCaptcha(linkId: string): Promise<number> {
    if (!this.cdp || !this.targetSessionId)
      throw new Error('CDP client or target session ID not initialized');

    // Extract main frame ID for target tab
    // const targetFrameTree = await this.cdp.Page.getFrameTree(undefined, this.targetSessionId);
    // const targetMainFrameId = targetFrameTree.frameTree.frame.id;

    // Create isolated world for target tab
    // const { executionContextId } = await this.cdp.Page.createIsolatedWorld(
    //   {
    //     frameId: targetMainFrameId,
    //     worldName: 'kaku-target-world',
    //     grantUniveralAccess: true,
    //   },
    //   this.targetSessionId,
    // );

    // Inject target tab scripts only (control tab scripts already injected early)
    this.log(
      'Injecting scripts for captcha handling with executionContextId:',
      this.executionContextId,
    );
    await this.injectTargetTabScripts(linkId, this.executionContextId!);

    return this.executionContextId!;
  }

  /**
   * Inject scripts for the target tab (user interaction)
   */
  private async injectTargetTabScripts(linkId: string, executionContextId: number): Promise<void> {
    if (!this.cdp || !this.targetSessionId) {
      throw new Error('Target tab not properly initialized');
    }

    // Get script URLs
    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const screenCropperUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'screen-cropper.min.js',
    );

    const captchaDetectorUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'captcha-detector.min.js',
    );

    const screenshotComparisonUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'screenshot-comparison.min.js',
    );

    const captchaDetectorTfUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'captcha-detector-tf.min.js',
    );

    const tfModelBundleUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'tf-model-bundle.min.js',
    );

    try {
      const layoutMetrics = await this.cdp.Page.getLayoutMetrics(undefined, this.targetSessionId);
      const viewport = {
        width: layoutMetrics.cssLayoutViewport.clientWidth,
        height: layoutMetrics.cssLayoutViewport.clientHeight,
      };

      // Inject core scripts in parallel
      await Promise.all([
        injectScript(this.cdp, crossTabCommUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, screenshotComparisonUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, screenCropperUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, captchaDetectorUrl, executionContextId, this.targetSessionId),
        injectTensorFlowJS(this.cdp, executionContextId, this.targetSessionId),
      ]);

      // Initialize proxy browser controller first (screen cropper depends on it)
      const initScript = `
        (async () => {
          await window.browserController.init();
        })();
      `;
      await this.cdp.Runtime.evaluate(
        {
          expression: initScript,
          contextId: executionContextId,
          awaitPromise: true,
        },
        this.targetSessionId,
      );
      // Inject TensorFlow-dependent scripts and initialize screen cropper
      await Promise.all([
        injectScript(this.cdp, captchaDetectorTfUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, tfModelBundleUrl, executionContextId, this.targetSessionId),
        initScreenCropper(
          this.cdp,
          `${this.env.KAKU_WS_ENDPOINT}/agents/connections/${linkId}`,
          executionContextId,
          { width: viewport.width, height: viewport.height },
          this.targetSessionId,
        ),
      ]);

      // Initialize TensorFlow detector
      await initTensorFlowDetector(this.cdp, executionContextId, viewport, this.targetSessionId);
    } catch (error) {
      this.error('Error injecting target tab scripts:', error);
      throw error;
    }
  }

  private async generateFormWithOpenAI(
    screenshot: string,
    platformId: PlatformTypes,
  ): Promise<PageStateResult> {
    const layoutMetrics = await this.cdp!.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const version = getPlatformVersion(platformId, this.env);

    const result = await makeParallelLLMCalls(
      this.llmService,
      {
        platform: platformId,
        screenshot,
        skipCache: this.env.SKIP_CACHE,
        viewportWidth: viewPort.width,
        viewportHeight: viewPort.height,
        version,
      },
      FORM_VISION_PROMPT_V6,
      FORM_VISION_CLASSIFICATION_PROMPT_V34,
    );
    console.log('Parallel LLM calls completed');

    return result;
  }

  private async acknowledgeExtractedForm(
    connectionDOName: string,
    formData: PageStateResult,
    screenshot: string,
  ): Promise<void> {
    const agent = this.env.Connections.idFromName(connectionDOName);
    const stub = this.env.Connections.get(agent);

    await stub.onFormStateChange(screenshot, {
      ...formData,
    });
  }

  private async executeFormActions(formSubmissionPayload: string): Promise<void> {
    try {
      this.log(
        '→ Starting form fill process with coordinate-based interaction',
        formSubmissionPayload,
      );
      const encryptionKey = await getEncryptionKey();
      const decrypted = await decryptData(formSubmissionPayload, encryptionKey);
      const actionsPayload = JSON.parse(decrypted as string) as FormSubmissionEventPayload;
      // Sort actions by order, ensuring submit actions are last
      const sortedActions = actionsPayload.actions.sort((a, b) => a.order - b.order);

      // Capture pre-submission screenshot for visual change detection
      this.preSubmissionScreenshot = await this.captureScreenshotForComparison();

      // Process each action in order
      for (const action of sortedActions) {
        await this.executeAction(action);
      }

      this.log('✓ Form actions executed successfully');
    } catch (error) {
      this.error(`✖ Error executing form actions: ${(error as Error).message}`);
      throw error;
    }
  }

  private async executeAIActions(
    formData: PageStateResult,
    elementCoordinateMapping?: ElementCoordinateMapping,
  ): Promise<void> {
    try {
      const aiActionsFields = formData.extractionResult.controls.fields.filter(
        (item) => item.isDontAskAgainControl,
      );
      if (aiActionsFields.length === 0) {
        this.log('No AI actions to execute');
        return;
      }

      aiActionsFields.map(async (aiActionField) => {
        await this.handleKeepMeLoggedIn(aiActionField, elementCoordinateMapping);
        return;
      });
    } catch (error) {
      this.error(`Error executing AI form actions: ${(error as Error).message}`);
      throw error;
    }
  }

  private async handleKeepMeLoggedIn(
    action: FormField,
    elementCoordinateMapping?: ElementCoordinateMapping,
  ): Promise<void> {
    if (action.checked) {
      // early exit, no need to click as the checkbox is already checked
      return;
    }
    const coordinates = elementCoordinateMapping?.[action.id];

    if (!coordinates) {
      throw new Error('No coordinates found for keep-me-logged-in action');
    }

    await this.clickAt(coordinates);
  }

  private async executeAction(action: Action): Promise<void> {
    const { type, coordinates, name, value } = action;

    if (type === 'fill' && value) {
      await this.fillTextField(coordinates, name, value);
    } else if (type === 'click' || type === 'select') {
      await this.clickAt(coordinates);
    }
  }

  private async fillTextField(
    coordinates: { x: number; y: number },
    name: string,
    value: string,
  ): Promise<void> {
    try {
      // Click on the field
      await this.clickAt(coordinates);

      // Triple-click to select all text
      await this.tripleClickAt(coordinates);

      // Clear any existing text
      await this.pressBackspace();

      // Type the value
      await this.typeText(value);
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async clickAt(coordinates: { x: number; y: number }): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    try {
      // Mouse down
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        this.targetSessionId,
      );

      // Mouse up to complete click
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        this.targetSessionId,
      );
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async tripleClickAt(coordinates: { x: number; y: number }): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    for (let i = 0; i < 3; i++) {
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        this.targetSessionId,
      );
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        this.targetSessionId,
      );
    }
  }

  private async pressBackspace(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    await this.cdp.Input.dispatchKeyEvent(
      {
        type: 'keyDown',
        windowsVirtualKeyCode: 8,
        key: 'Backspace',
      },
      this.targetSessionId,
    );
    await this.cdp.Input.dispatchKeyEvent(
      {
        type: 'keyUp',
        key: 'Backspace',
      },
      this.targetSessionId,
    );
  }

  private async typeText(text: string): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    try {
      // Set the clipboard content
      await this.cdp.Input.insertText(
        {
          text: text,
        },
        this.targetSessionId,
      );

      this.log(
        `✓ Successfully pasted text: "${text.substring(0, 2)}${text.length > 4 ? '...' : ''}"`,
      );
    } catch (error) {
      throw Error(`✖ Error pasting text: ${(error as Error).message}`);
    }
  }

  private async waitForPageUpdateAfterSubmission(
    config: PlatformDetectionConfig,
    currentFormVisionResult: FormVisionResult,
  ): Promise<StateChangeType | null> {
    if (!this.cdp || !this.platformId) return null;

    // Strategy 1: Traditional page load (for legacy platforms)
    const pageLoad = this.waitForPageLoad().then((result) => {
      if (result === true) {
        this.log('✓ Page load event fired - cancelling visual change detection');
        this.stateChangeAbortController.abort();
        return StateChangeType.PAGE_LOAD;
      }
    });

    // Strategy 2: Platform-aware visual change detection (for SPAs)
    const llmDetection = this.compareVisualChangesUsingLLM(
      config,
      this.platformId,
      currentFormVisionResult,
    ).then((result) => {
      if (result === true) {
        this.stateChangeAbortController.abort();
        return StateChangeType.LLM_DETECTION;
      }
    });

    /*const visualChanges = this.waitForVisualChangesWithPlatformConfig(
        config,
    ).then((result) => {
      if(result === true){
        this.stateChangeAbortController.abort();
        return StateChangeType.VISUAL_CHANGES;
      }
    })*/

    // Race all strategies with platform-specific timeout
    const result = await withTimeout(
      Promise.race([pageLoad, llmDetection /*, visualChanges*/]),
      config.maxWaitTime,
    );

    return typeof result === 'string' ? (result as StateChangeType) : null;
  }

  /**
   * Platform-aware visual change detection with stability checks
   * Handles different loading behaviors across platforms, especially Facebook's intermediate screens
   * @param config Platform-specific detection configuration
   * @param abortSignal Signal to cancel the detection when page load event fires
   * @returns Promise that resolves when stable visual changes are detected or timeout is reached
   */
  private async waitForVisualChangesWithPlatformConfig(
    config: PlatformDetectionConfig,
  ): Promise<boolean | null> {
    if (!this.cdp || !this.targetSessionId) return null;

    // If no pre-submission screenshot was captured, skip visual change detection
    if (!this.preSubmissionScreenshot) {
      this.error('→ No pre-submission screenshot available, skipping visual change detection');
      return null;
    }

    const startTime = Date.now();
    const {
      changeThreshold,
      checkInterval,
      maxWaitTime,
      stabilityChecks = 0,
      minChangeDetectionDelay = 0,
    } = config;

    this.log(
      `→ Start monitoring for visual changes (platform: ${this.platformId}, threshold: ${changeThreshold}%)`,
    );

    // Wait for minimum delay before starting to check for visual changes
    await sleep(minChangeDetectionDelay);

    return new Promise<boolean | null>((resolve) => {
      let firstChangeDetected = false;
      let firstChangeTime = 0;
      let lastStableScreenshot: string | null = null;
      let consecutiveStableChecks = 0;

      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        console.log('Visual change State change listener cancelled by abort controller');
        resolve(null);
      });

      const checkForChanges = async () => {
        try {
          if (Date.now() - startTime > maxWaitTime) {
            this.log('→ Visual change detection timeout reached');
            resolve(null);
            return;
          }

          const currentScreenshot = await this.captureScreenshotForComparison();
          if (!currentScreenshot) {
            this.warn('Failed to capture screenshot for visual change detection');
            setTimeout(checkForChanges, checkInterval);
            return;
          }

          // First, check if we haven't detected any change yet
          if (!firstChangeDetected) {
            const comparisonResult = await compareScreenshots(
              this.preSubmissionScreenshot!,
              currentScreenshot,
            );
            if (comparisonResult && comparisonResult.percentageDiff > changeThreshold) {
              // First significant change detected
              firstChangeDetected = true;
              firstChangeTime = Date.now();
              lastStableScreenshot = currentScreenshot;

              this.log(
                `→ First visual change detected: ${comparisonResult.percentageDiff.toFixed(2)}% difference`,
              );

              // If no stability checks required, resolve immediately
              if (stabilityChecks === 0) {
                this.log('✓ Visual change confirmed (no stability checks required)');
                resolve(true);
                return;
              }
            }
          } else {
            // After first change detected, monitor for stability
            this.log(`→ Monitoring for stability (change detected, checking for settlement)`);

            // Check for stability after first change
            const stabilityResult = await compareScreenshots(
              lastStableScreenshot!,
              currentScreenshot,
            );

            if (stabilityResult && stabilityResult.percentageDiff < 0.5) {
              consecutiveStableChecks++;
              this.log(
                `→ Stability check ${consecutiveStableChecks}/${stabilityChecks} (${stabilityResult.percentageDiff.toFixed(2)}% diff)`,
              );

              if (consecutiveStableChecks >= stabilityChecks) {
                const totalTime = Date.now() - firstChangeTime;
                this.log(
                  `✓ Visual change confirmed with stability (${totalTime}ms after first change)`,
                );
                resolve(true);
                return;
              }
            } else {
              // Content still changing, reset stability counter and update reference
              consecutiveStableChecks = 0;
              lastStableScreenshot = currentScreenshot;
              this.log(
                `→ Content still changing (${stabilityResult?.percentageDiff.toFixed(2)}% diff), resetting stability counter`,
              );
            }
          }

          setTimeout(checkForChanges, checkInterval);
        } catch (error) {
          this.warn('Error during visual change detection:', error);
          setTimeout(checkForChanges, checkInterval);
        }
      };
      checkForChanges();
    });
  }

  private async compareVisualChangesUsingLLM(
    config: PlatformDetectionConfig,
    platformId: PlatformTypes,
    currentFormVisionResult: FormVisionResult,
  ): Promise<boolean | null> {
    if (!this.cdp || !this.targetSessionId) return null;

    const layoutMetrics = await this.cdp!.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const version = getPlatformVersion(platformId, this.env);

    const startTime = Date.now();
    const { checkInterval, maxWaitTime, minChangeDetectionDelay = 0 } = config;

    // Wait for minimum delay before starting to check for LLM changes
    await sleep(minChangeDetectionDelay);

    return new Promise<boolean | null>(async (resolve) => {
      let resultFound = false;
      this.stateChangeAbortController.signal.addEventListener('abort', () => {
        console.log('LLM State change listener cancelled by abort controller');
        resultFound = true;
        resolve(null);
      });
      while (!resultFound) {
        if (Date.now() - startTime > maxWaitTime) {
          console.log('→ LLM change detection timeout reached', resultFound);
          resultFound = true;
          resolve(null);
        }
        const currentScreenshot = await this.captureScreenshotForComparison();
        if (currentScreenshot) {
          const comparisonResult = await this.llmService.detectStateChangeFromUserAgentState({
            platform: platformId,
            prompt: pageStateResultComparisonPromptInstructions,
            agentVisionResultState: currentFormVisionResult,
            screenshot: currentScreenshot,
            skipCache: this.env.SKIP_CACHE,
            viewportWidth: viewPort.width,
            viewportHeight: viewPort.height,
            version: version,
          });
          if (comparisonResult) {
            resultFound = true;
            resolve(true);
          }
        }
        await sleep(checkInterval);
      }

      resolve(null);
    });
  }

  /**
   * Captures a screenshot optimized for comparison
   * Returns base64 PNG string for direct pixelmatch processing
   */
  private async captureScreenshotForComparison(): Promise<string | null> {
    if (!this.cdp || !this.targetSessionId) {
      throw new Error('CDP client or target session ID not initialized');
    }

    const screenshot = await this.cdp.Page.captureScreenshot(
      {
        format: 'png',
        captureBeyondViewport: false,
      },
      this.targetSessionId,
    );

    return screenshot.data;
  }

  private checkIfScreenIsATwoFactorAuthScreen(classificationResult: ClassificationResult): boolean {
    return classificationResult.screenInfo.screenClass === 'multi-factor-verification-screen';
  }

  private async cleanupResources(): Promise<void> {
    if (this.browserSession?.sessionId) {
      await this.browserService.closeSession(this.browserSession.sessionId);
    } else {
      this.warn('No browser session to close');
    }
  }

  private async handleOnAuthenticated(
    event: WorkflowEvent<ConnectionsWorkflowParams>,
    latestScreenshot: string,
  ) {
    this.log('✓ Workflow completed: user authenticated');
    if (this.cdp) {
      const browserDataAdapter = new CDPBrowserDataAdapter(this.cdp, this.targetSessionId);
      await this.browserStateService.updateBrowserState(
        browserDataAdapter,
        event.payload.userId,
        event.payload.platformId,
      );
    }
    await this.cleanupResources();

    //Save the screenshot to R2
    storeConnectionScreenshotToR2(
      this.env.SCREENSHOTS_INBOUND_BUCKET,
      event.payload.userId,
      event.payload.platformId,
      event.payload.sessionId,
      ConnectionWorkflowState.Authenticated,
      latestScreenshot,
    );
  }

  private async handleWorkflowStepError(
    workflowStepName: WorkflowStepName,
    error: any,
    payload: ConnectionsWorkflowParams,
  ): Promise<void> {
    await ErrorService.handleWorkflowStepError(
      workflowStepName,
      error,
      {
        ...payload,
        referenceId: payload.linkId,
      },
      this.env,
    );
    await this.cleanupResources();
  }
}
