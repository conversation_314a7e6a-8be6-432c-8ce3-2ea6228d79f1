/**
 * Classification interfaces for screen classification
 */

export interface ClassificationResult {
  screenInfo: ClassificationMetadata;
}

export interface ClassificationMetadata {
  title: string;
  instruction: string;
  authState: 'authenticated' | 'not-authenticated';
  errors: string[] | null;
  classificationReasoning: string;
  screenClass: ScreenClass;
  verificationCode: string | null;
}

export type ScreenClass =
  | 'profile-management-screen'
  | 'multi-factor-verification-screen'
  | 'passkey-screen'
  | 'captcha-screen'
  | 'loading-screen'
  | 'trust-device-screen'
  | 'other';
