/**
 * Core form interfaces for HTMX form generation
 */

export interface ExtractionResult {
  screenInfo: FormMetadata;
  controls: FormControls;
}

export interface FormMetadata {
  errors: string[] | null;
}

export interface ControlVisibilityRule {
  id: string;
  status: 'included' | 'excluded';
  reason?: string | null;
}

export interface FormControls {
  fields: FormField[];
  buttons: FormButton[];
}

export interface FormField {
  id: string;
  order: number;
  label: string;
  isLikelyDropdown: boolean;
  fieldControlType:
    | 'select'
    | 'dropdown'
    | 'text'
    | 'password'
    | 'number'
    | 'checkbox'
    | 'checkboxgroup'
    | 'radio'
    | 'textarea'
    | 'other';
  actiontype: 'fill' | 'select';
  name: string;
  checked: boolean;
  isDontAskAgainControl?: boolean;
  options?: { value: string; label: string }[] | null;
}

export interface FormButton {
  id: string;
  order: number;
  label: string;
  variant: 'primary' | 'secondary' | 'link';
  type: 'submit' | 'click' | 'device-ack';
  actiontype: 'click';
  isDontAskAgainControl?: boolean;
}
